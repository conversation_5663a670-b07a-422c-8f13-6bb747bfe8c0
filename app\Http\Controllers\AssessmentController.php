<?php

namespace App\Http\Controllers;

use App\Models\Assessment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AssessmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Assessment::query();

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $assessments = $query->with('questions')->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $assessments
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:career,degree,personality,skill',
            'time_limit' => 'nullable|integer|min:1',
            'scoring_rules' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $assessment = Assessment::create([
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'time_limit' => $request->time_limit,
            'scoring_rules' => $request->scoring_rules,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Assessment created successfully',
            'data' => $assessment->load('questions')
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Assessment $assessment)
    {
        return response()->json([
            'success' => true,
            'data' => $assessment->load(['questions' => function($query) {
                $query->orderBy('order');
            }])
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Assessment $assessment)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'type' => 'sometimes|in:career,degree,personality,skill',
            'time_limit' => 'nullable|integer|min:1',
            'scoring_rules' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $assessment->update($request->only([
            'title', 'description', 'type', 'time_limit', 'scoring_rules', 'is_active'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Assessment updated successfully',
            'data' => $assessment->load('questions')
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Assessment $assessment)
    {
        $assessment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Assessment deleted successfully'
        ]);
    }

    /**
     * Get assessment questions
     */
    public function questions(Assessment $assessment)
    {
        $questions = $assessment->questions()->orderBy('order')->get();

        return response()->json([
            'success' => true,
            'data' => $questions
        ]);
    }

    /**
     * Start assessment for user
     */
    public function start(Request $request, Assessment $assessment)
    {
        if (!$assessment->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Assessment is not active'
            ], 400);
        }

        $user = $request->user();
        
        // Check if user already completed this assessment
        $existingResult = $user->testResults()
            ->where('assessment_id', $assessment->id)
            ->exists();

        if ($existingResult) {
            return response()->json([
                'success' => false,
                'message' => 'You have already completed this assessment'
            ], 400);
        }

        $questions = $assessment->questions()
            ->where('is_required', true)
            ->orderBy('order')
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Assessment started successfully',
            'data' => [
                'assessment' => $assessment,
                'questions' => $questions,
                'total_questions' => $questions->count(),
                'time_limit' => $assessment->time_limit
            ]
        ]);
    }

    /**
     * Get assessment statistics
     */
    public function statistics(Assessment $assessment)
    {
        $stats = [
            'total_participants' => $assessment->testResults()->count(),
            'average_score' => $assessment->testResults()->avg('percentage'),
            'completion_rate' => $assessment->testResults()->count() / max(1, $assessment->userResponses()->distinct('user_id')->count()),
            'top_performers' => $assessment->testResults()
                ->with('user:id,name')
                ->orderBy('percentage', 'desc')
                ->limit(10)
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
