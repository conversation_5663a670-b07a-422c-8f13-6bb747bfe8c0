<?php

namespace App\Http\Controllers;

use App\Models\Career;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CareerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Career::with('category');

        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by experience level
        if ($request->has('experience_level')) {
            $query->where('experience_level', $request->experience_level);
        }

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('requirements', 'like', "%{$search}%");
            });
        }

        $careers = $query->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $careers
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'required|exists:categories,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'nullable|string',
            'required_skills' => 'nullable|array',
            'salary_range' => 'nullable|string|max:100',
            'experience_level' => 'nullable|string|max:50',
            'growth_prospects' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $career = Career::create([
            'category_id' => $request->category_id,
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'description' => $request->description,
            'requirements' => $request->requirements,
            'required_skills' => $request->required_skills,
            'salary_range' => $request->salary_range,
            'experience_level' => $request->experience_level,
            'growth_prospects' => $request->growth_prospects,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Career created successfully',
            'data' => $career->load('category')
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Career $career)
    {
        return response()->json([
            'success' => true,
            'data' => $career->load('category')
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Career $career)
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'sometimes|exists:categories,id',
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'requirements' => 'nullable|string',
            'required_skills' => 'nullable|array',
            'salary_range' => 'nullable|string|max:100',
            'experience_level' => 'nullable|string|max:50',
            'growth_prospects' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only([
            'category_id', 'title', 'description', 'requirements', 
            'required_skills', 'salary_range', 'experience_level', 
            'growth_prospects', 'is_active'
        ]);

        if ($request->has('title')) {
            $updateData['slug'] = Str::slug($request->title);
        }

        $career->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Career updated successfully',
            'data' => $career->load('category')
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Career $career)
    {
        $career->delete();

        return response()->json([
            'success' => true,
            'message' => 'Career deleted successfully'
        ]);
    }

    /**
     * Get career recommendations for user
     */
    public function recommendations(Request $request)
    {
        $user = $request->user();
        
        // Get user's latest test results
        $latestResults = $user->testResults()
            ->with('assessment')
            ->whereHas('assessment', function($query) {
                $query->where('type', 'career');
            })
            ->latest()
            ->first();

        if (!$latestResults || !$latestResults->recommended_careers) {
            return response()->json([
                'success' => false,
                'message' => 'No career assessment results found'
            ], 404);
        }

        $recommendedTitles = collect($latestResults->recommended_careers)
            ->pluck('title')
            ->toArray();

        $careers = Career::whereIn('title', $recommendedTitles)
            ->where('is_active', true)
            ->with('category')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'careers' => $careers,
                'test_result' => $latestResults
            ]
        ]);
    }

    /**
     * Search careers by skills
     */
    public function searchBySkills(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'skills' => 'required|array',
            'skills.*' => 'string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $skills = $request->skills;
        
        $careers = Career::where('is_active', true)
            ->where(function($query) use ($skills) {
                foreach ($skills as $skill) {
                    $query->orWhereJsonContains('required_skills', $skill);
                }
            })
            ->with('category')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $careers
        ]);
    }

    /**
     * Get trending careers
     */
    public function trending()
    {
        // This could be based on view counts, applications, etc.
        $careers = Career::where('is_active', true)
            ->with('category')
            ->inRandomOrder()
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $careers
        ]);
    }
}
