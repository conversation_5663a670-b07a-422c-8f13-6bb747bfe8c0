<?php

namespace App\Http\Controllers;

use App\Models\UserProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class UserProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Only for admin users
        if (!$request->user()->hasRole('admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $profiles = UserProfile::with('user')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $profiles
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = $request->user();
        
        // Check if profile already exists
        if ($user->profile) {
            return response()->json([
                'success' => false,
                'message' => 'Profile already exists'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'nullable|string|max:100',
            'last_name' => 'nullable|string|max:100',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'phone' => 'nullable|string|max:20',
            'city' => 'nullable|string|max:100',
            'education_level' => 'nullable|string|max:100',
            'current_job' => 'nullable|string|max:100',
            'experience_years' => 'nullable|integer|min:0|max:50',
            'interests' => 'nullable|array',
            'current_skills' => 'nullable|array',
            'bio' => 'nullable|string|max:1000',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $profileData = $request->only([
            'first_name', 'last_name', 'birth_date', 'gender', 
            'phone', 'city', 'education_level', 'current_job', 
            'experience_years', 'interests', 'current_skills', 'bio'
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $profileData['avatar'] = $avatarPath;
        }

        $profileData['user_id'] = $user->id;

        $profile = UserProfile::create($profileData);

        return response()->json([
            'success' => true,
            'message' => 'Profile created successfully',
            'data' => $profile
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, UserProfile $userProfile = null)
    {
        // If no profile ID provided, return current user's profile
        if (!$userProfile) {
            $userProfile = $request->user()->profile;
        }

        if (!$userProfile) {
            return response()->json([
                'success' => false,
                'message' => 'Profile not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $userProfile->load('user:id,name,email')
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, UserProfile $userProfile = null)
    {
        $user = $request->user();
        
        // If no profile specified, use current user's profile
        if (!$userProfile) {
            $userProfile = $user->profile;
        }

        // Check if user owns this profile or is admin
        if ($userProfile->user_id !== $user->id && !$user->hasRole('admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|string|max:100',
            'last_name' => 'sometimes|string|max:100',
            'birth_date' => 'sometimes|date|before:today',
            'gender' => 'sometimes|in:male,female,other',
            'phone' => 'sometimes|string|max:20',
            'city' => 'sometimes|string|max:100',
            'education_level' => 'sometimes|string|max:100',
            'current_job' => 'sometimes|string|max:100',
            'experience_years' => 'sometimes|integer|min:0|max:50',
            'interests' => 'sometimes|array',
            'current_skills' => 'sometimes|array',
            'bio' => 'sometimes|string|max:1000',
            'avatar' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only([
            'first_name', 'last_name', 'birth_date', 'gender', 
            'phone', 'city', 'education_level', 'current_job', 
            'experience_years', 'interests', 'current_skills', 'bio'
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar
            if ($userProfile->avatar) {
                Storage::disk('public')->delete($userProfile->avatar);
            }
            
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $updateData['avatar'] = $avatarPath;
        }

        $userProfile->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => $userProfile
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, UserProfile $userProfile)
    {
        $user = $request->user();

        // Check if user owns this profile or is admin
        if ($userProfile->user_id !== $user->id && !$user->hasRole('admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Delete avatar file
        if ($userProfile->avatar) {
            Storage::disk('public')->delete($userProfile->avatar);
        }

        $userProfile->delete();

        return response()->json([
            'success' => true,
            'message' => 'Profile deleted successfully'
        ]);
    }

    /**
     * Get current user's profile
     */
    public function me(Request $request)
    {
        $user = $request->user();
        $profile = $user->profile;

        if (!$profile) {
            return response()->json([
                'success' => false,
                'message' => 'Profile not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $profile
        ]);
    }

    /**
     * Update current user's avatar
     */
    public function updateAvatar(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $profile = $user->profile;

        if (!$profile) {
            return response()->json([
                'success' => false,
                'message' => 'Profile not found'
            ], 404);
        }

        // Delete old avatar
        if ($profile->avatar) {
            Storage::disk('public')->delete($profile->avatar);
        }

        // Store new avatar
        $avatarPath = $request->file('avatar')->store('avatars', 'public');
        
        $profile->update(['avatar' => $avatarPath]);

        return response()->json([
            'success' => true,
            'message' => 'Avatar updated successfully',
            'data' => ['avatar_url' => Storage::url($avatarPath)]
        ]);
    }

    /**
     * Get profile completion percentage
     */
    public function completionPercentage(Request $request)
    {
        $user = $request->user();
        $profile = $user->profile;

        if (!$profile) {
            return response()->json([
                'success' => true,
                'data' => ['completion_percentage' => 0]
            ]);
        }

        $fields = [
            'first_name', 'last_name', 'birth_date', 'gender', 
            'phone', 'city', 'education_level', 'current_job', 
            'experience_years', 'interests', 'current_skills', 
            'bio', 'avatar'
        ];

        $filledFields = 0;
        foreach ($fields as $field) {
            if (!empty($profile->$field)) {
                $filledFields++;
            }
        }

        $percentage = round(($filledFields / count($fields)) * 100);

        return response()->json([
            'success' => true,
            'data' => [
                'completion_percentage' => $percentage,
                'filled_fields' => $filledFields,
                'total_fields' => count($fields)
            ]
        ]);
    }
}
