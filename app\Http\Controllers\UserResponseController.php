<?php

namespace App\Http\Controllers;

use App\Models\UserResponse;
use App\Models\Assessment;
use App\Models\Question;
use App\Models\TestResult;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class UserResponseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $responses = $user->userResponses()
            ->with(['assessment', 'question'])
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $responses
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'assessment_id' => 'required|exists:assessments,id',
            'question_id' => 'required|exists:questions,id',
            'answer' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $assessment = Assessment::findOrFail($request->assessment_id);
        $question = Question::findOrFail($request->question_id);

        // Check if question belongs to assessment
        if ($question->assessment_id !== $assessment->id) {
            return response()->json([
                'success' => false,
                'message' => 'Question does not belong to this assessment'
            ], 400);
        }

        // Check if user already answered this question
        $existingResponse = UserResponse::where([
            'user_id' => $user->id,
            'question_id' => $question->id
        ])->first();

        if ($existingResponse) {
            return response()->json([
                'success' => false,
                'message' => 'You have already answered this question'
            ], 400);
        }

        // Calculate score based on question type
        $score = $this->calculateScore($question, $request->answer);

        $response = UserResponse::create([
            'user_id' => $user->id,
            'assessment_id' => $assessment->id,
            'question_id' => $question->id,
            'answer' => $request->answer,
            'score' => $score,
            'answered_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Response recorded successfully',
            'data' => $response
        ], 201);
    }

    /**
     * Submit entire assessment
     */
    public function submitAssessment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'assessment_id' => 'required|exists:assessments,id',
            'responses' => 'required|array',
            'responses.*.question_id' => 'required|exists:questions,id',
            'responses.*.answer' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $assessment = Assessment::findOrFail($request->assessment_id);

        // Check if user already completed this assessment
        $existingResult = TestResult::where([
            'user_id' => $user->id,
            'assessment_id' => $assessment->id
        ])->exists();

        if ($existingResult) {
            return response()->json([
                'success' => false,
                'message' => 'Assessment already completed'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $totalScore = 0;
            $maxScore = 0;
            $responses = [];

            foreach ($request->responses as $responseData) {
                $question = Question::findOrFail($responseData['question_id']);
                
                // Verify question belongs to assessment
                if ($question->assessment_id !== $assessment->id) {
                    throw new \Exception('Invalid question for this assessment');
                }

                $score = $this->calculateScore($question, $responseData['answer']);
                
                $response = UserResponse::create([
                    'user_id' => $user->id,
                    'assessment_id' => $assessment->id,
                    'question_id' => $question->id,
                    'answer' => $responseData['answer'],
                    'score' => $score,
                    'answered_at' => now()
                ]);

                $responses[] = $response;
                $totalScore += $score;
                $maxScore += ($question->weight * 100); // Assuming max score per question is 100
            }

            // Calculate percentage
            $percentage = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 0;

            // Create test result
            $testResult = TestResult::create([
                'user_id' => $user->id,
                'assessment_id' => $assessment->id,
                'total_score' => $totalScore,
                'percentage' => $percentage,
                'completed_at' => now()
            ]);

            // Process results based on assessment type
            $analysis = $this->processResults($testResult, $assessment, $responses);
            
            $testResult->update([
                'skill_scores' => $analysis['skill_scores'] ?? null,
                'recommended_careers' => $analysis['recommended_careers'] ?? null,
                'recommended_degrees' => $analysis['recommended_degrees'] ?? null,
                'skill_gaps' => $analysis['skill_gaps'] ?? null,
                'analysis_summary' => $analysis['summary'] ?? null
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Assessment submitted successfully',
                'data' => [
                    'test_result' => $testResult,
                    'responses' => $responses,
                    'analysis' => $analysis
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit assessment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(UserResponse $userResponse)
    {
        return response()->json([
            'success' => true,
            'data' => $userResponse->load(['assessment', 'question', 'user'])
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Get user responses for specific assessment
     */
    public function getAssessmentResponses(Request $request, $assessmentId)
    {
        $user = $request->user();
        $responses = UserResponse::where([
            'user_id' => $user->id,
            'assessment_id' => $assessmentId
        ])->with(['question', 'assessment'])->get();

        return response()->json([
            'success' => true,
            'data' => $responses
        ]);
    }

    /**
     * Calculate score based on question type and answer
     */
    private function calculateScore($question, $answer)
    {
        $baseScore = 0;
        
        switch ($question->question_type) {
            case 'rating':
                // For rating questions (1-5 scale)
                $baseScore = intval($answer) * 20; // Convert to 0-100 scale
                break;
                
            case 'multiple_choice':
                // For multiple choice, you might have scoring in options
                $options = $question->options;
                if (isset($options['scoring'][$answer])) {
                    $baseScore = $options['scoring'][$answer];
                } else {
                    $baseScore = 50; // Default score
                }
                break;
                
            case 'boolean':
                $baseScore = $answer === 'true' || $answer === true ? 100 : 0;
                break;
                
            default:
                $baseScore = 50; // Default for text questions
        }
        
        // Apply question weight
        return $baseScore * $question->weight;
    }

    /**
     * Process assessment results and generate recommendations
     */
    private function processResults($testResult, $assessment, $responses)
    {
        $analysis = [
            'skill_scores' => [],
            'recommended_careers' => [],
            'recommended_degrees' => [],
            'skill_gaps' => [],
            'summary' => ''
        ];

        // This is a simplified analysis - you would implement more sophisticated logic
        switch ($assessment->type) {
            case 'career':
                $analysis['summary'] = 'Based on your responses, you show strong aptitude for analytical thinking and problem-solving.';
                $analysis['recommended_careers'] = [
                    ['title' => 'Software Developer', 'match_percentage' => 85],
                    ['title' => 'Data Analyst', 'match_percentage' => 78],
                    ['title' => 'Product Manager', 'match_percentage' => 72]
                ];
                break;
                
            case 'skill':
                $analysis['skill_scores'] = [
                    'Technical Skills' => $testResult->percentage * 0.8,
                    'Communication' => $testResult->percentage * 0.9,
                    'Leadership' => $testResult->percentage * 0.7
                ];
                break;
        }

        return $analysis;
    }
}
