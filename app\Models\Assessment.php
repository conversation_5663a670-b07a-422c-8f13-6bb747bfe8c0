<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Assessment extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description', 
        'type',
        'time_limit',
        'total_questions',
        'is_active',
        'scoring_rules'
    ];

    protected $casts = [
        'scoring_rules' => 'array',
        'is_active' => 'boolean'
    ];

    // Relations
    public function questions()
    {
        return $this->hasMany(Question::class);
    }

    public function userResponses()
    {
        return $this->hasMany(UserResponse::class);
    }

    public function testResults()
    {
        return $this->hasMany(TestResult::class);
    }
}
