<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Career extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'title',
        'slug',
        'description',
        'requirements',
        'required_skills',
        'salary_range',
        'experience_level',
        'growth_prospects',
        'is_active'
    ];

    protected $casts = [
        'required_skills' => 'array',
        'is_active' => 'boolean'
    ];

    // Relations
    public function category()
    {
        return $this->belongsTo(Category::class);
    }
}
