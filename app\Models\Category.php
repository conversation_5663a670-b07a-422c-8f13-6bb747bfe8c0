<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug', 
        'description',
        'color',
        'icon',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    // Relations
    public function skills()
    {
        return $this->hasMany(Skill::class);
    }

    public function careers()
    {
        return $this->hasMany(Career::class);
    }

    public function degrees()
    {
        return $this->hasMany(Degree::class);
    }

    public function blogs()
    {
        return $this->hasMany(Blog::class);
    }
}
