<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Degree extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'title',
        'slug',
        'description',
        'level',
        'duration_months',
        'career_paths',
        'admission_requirements',
        'average_salary',
        'is_active'
    ];

    protected $casts = [
        'career_paths' => 'array',
        'is_active' => 'boolean'
    ];

    // Relations
    public function category()
    {
        return $this->belongsTo(Category::class);
    }
}
