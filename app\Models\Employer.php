<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Employer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'company_name',
        'company_website',
        'company_description',
        'industry',
        'company_size',
        'location',
        'phone',
        'logo',
        'verification_status',
        'verified_at'
    ];

    protected $casts = [
        'verified_at' => 'datetime'
    ];

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
