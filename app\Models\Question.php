<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;

    protected $fillable = [
        'assessment_id',
        'question_text',
        'question_type',
        'options',
        'weight',
        'order',
        'is_required'
    ];

    protected $casts = [
        'options' => 'array',
        'is_required' => 'boolean'
    ];

    // Relations
    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }

    public function userResponses()
    {
        return $this->hasMany(UserResponse::class);
    }
}
