<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Skill extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'description',
        'level',
        'importance_weight',
        'is_technical'
    ];

    protected $casts = [
        'is_technical' => 'boolean'
    ];

    // Relations
    public function category()
    {
        return $this->belongsTo(Category::class);
    }
}
