<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TestResult extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'assessment_id',
        'total_score',
        'percentage',
        'skill_scores',
        'recommended_careers',
        'recommended_degrees',
        'skill_gaps',
        'analysis_summary',
        'completed_at'
    ];

    protected $casts = [
        'skill_scores' => 'array',
        'recommended_careers' => 'array',
        'recommended_degrees' => 'array',
        'skill_gaps' => 'array',
        'completed_at' => 'datetime'
    ];

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }
}
