<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'birth_date',
        'gender',
        'phone',
        'city',
        'education_level',
        'current_job',
        'experience_years',
        'interests',
        'current_skills',
        'avatar',
        'bio'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'interests' => 'array',
        'current_skills' => 'array'
    ];

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
