<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessments', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->enum('type', ['career', 'degree', 'personality', 'skill']);
            $table->integer('time_limit')->nullable(); // زمان آزمون به دقیقه
            $table->integer('total_questions')->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('scoring_rules')->nullable(); // قوانین امتیازدهی
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessments');
    }
};
