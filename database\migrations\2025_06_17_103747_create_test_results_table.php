<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('test_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('assessment_id')->constrained()->onDelete('cascade');
            $table->integer('total_score');
            $table->decimal('percentage', 5, 2); // درصد نمره
            $table->json('skill_scores')->nullable(); // امتیاز مهارت‌های مختلف
            $table->json('recommended_careers')->nullable(); // مشاغل پیشنهادی
            $table->json('recommended_degrees')->nullable(); // رشته‌های پیشنهادی
            $table->json('skill_gaps')->nullable(); // شکاف‌های مهارتی
            $table->text('analysis_summary')->nullable(); // خلاصه تحلیل
            $table->timestamp('completed_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('test_results');
    }
};
