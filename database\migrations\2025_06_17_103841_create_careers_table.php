<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('careers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('requirements')->nullable(); // الزامات شغلی
            $table->json('required_skills')->nullable(); // مهارت‌های مورد نیاز
            $table->string('salary_range')->nullable(); // محدوده حقوق
            $table->string('experience_level')->nullable(); // سطح تجربه
            $table->text('growth_prospects')->nullable(); // چشم‌انداز رشد
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('careers');
    }
};
