<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('degrees', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->enum('level', ['associate', 'bachelor', 'master', 'phd', 'certificate']);
            $table->integer('duration_months')->nullable(); // مدت تحصیل به ماه
            $table->json('career_paths')->nullable(); // مسیرهای شغلی مرتبط
            $table->text('admission_requirements')->nullable(); // شرایط پذیرش
            $table->string('average_salary')->nullable(); // میانگین حقوق فارغ‌التحصیلان
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('degrees');
    }
};
