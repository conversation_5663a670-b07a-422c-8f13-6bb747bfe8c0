<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // First seed roles and permissions
        $this->call([
            RolesAndPermissionsSeeder::class,
        ]);

        // Create test admin user
        $admin = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);
        $admin->assignRole('admin');

        // Create test regular user
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
        $user->assignRole('user');

        // Create test employer
        $employer = User::factory()->create([
            'name' => 'Test Employer',
            'email' => '<EMAIL>',
        ]);
        $employer->assignRole('employer');
    }
}
