<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User permissions
            'view users',
            'create users',
            'update users',
            'delete users',
            
            // Assessment permissions
            'view assessments',
            'create assessments',
            'update assessments',
            'delete assessments',
            'take assessments',
            
            // Career permissions
            'view careers',
            'create careers',
            'update careers',
            'delete careers',
            'get career recommendations',
            
            // Degree permissions
            'view degrees',
            'create degrees',
            'update degrees',
            'delete degrees',
            'get degree recommendations',
            
            // Skill permissions
            'view skills',
            'create skills',
            'update skills',
            'delete skills',
            
            // Category permissions
            'view categories',
            'create categories',
            'update categories',
            'delete categories',
            
            // Blog permissions
            'view blogs',
            'create blogs',
            'update blogs',
            'delete blogs',
            'publish blogs',
            
            // Profile permissions
            'view profiles',
            'update own profile',
            'view other profiles',
            
            // Employer permissions
            'view candidates',
            'create job posts',
            'manage company profile',
            'verify employer',
            
            // Admin permissions
            'access admin panel',
            'view analytics',
            'manage system settings',
            'manage all content',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // User Role
        $userRole = Role::create(['name' => 'user']);
        $userRole->givePermissionTo([
            'take assessments',
            'view careers',
            'view degrees',
            'view skills',
            'view categories',
            'view blogs',
            'update own profile',
            'get career recommendations',
            'get degree recommendations',
        ]);

        // Employer Role
        $employerRole = Role::create(['name' => 'employer']);
        $employerRole->givePermissionTo([
            'take assessments',
            'view careers',
            'view degrees',
            'view skills',
            'view categories',
            'view blogs',
            'update own profile',
            'view candidates',
            'create job posts',
            'manage company profile',
            'get career recommendations',
            'get degree recommendations',
        ]);

        // Admin Role
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        $this->command->info('Roles and permissions created successfully!');
    }
}
