<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AssessmentController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\UserResponseController;
use App\Http\Controllers\TestResultController;
use App\Http\Controllers\SkillController;
use App\Http\Controllers\CareerController;
use App\Http\Controllers\DegreeController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\EmployerController;
use App\Http\Controllers\UserProfileController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Admin\UserController as AdminUserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public Routes (No Authentication Required)
Route::prefix('v1')->group(function () {
    
    // Authentication Routes
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
    });

    // Public Content Routes
    Route::get('categories', [CategoryController::class, 'index']);
    Route::get('categories/{category}', [CategoryController::class, 'show']);
    Route::get('categories/{category}/careers', [CategoryController::class, 'careers']);
    Route::get('categories/{category}/degrees', [CategoryController::class, 'degrees']);
    Route::get('categories/{category}/skills', [CategoryController::class, 'skills']);
    
    Route::get('careers', [CareerController::class, 'index']);
    Route::get('careers/{career}', [CareerController::class, 'show']);
    Route::get('careers/trending', [CareerController::class, 'trending']);
    Route::post('careers/search-by-skills', [CareerController::class, 'searchBySkills']);
    
    Route::get('degrees', [DegreeController::class, 'index']);
    Route::get('degrees/{degree}', [DegreeController::class, 'show']);
    
    Route::get('skills', [SkillController::class, 'index']);
    Route::get('skills/{skill}', [SkillController::class, 'show']);
    
    Route::get('blogs', [BlogController::class, 'index']);
    Route::get('blogs/{blog}', [BlogController::class, 'show']);
    
    Route::get('assessments', [AssessmentController::class, 'index']);
    Route::get('assessments/{assessment}', [AssessmentController::class, 'show']);
});

// Protected Routes (Authentication Required)
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    
    // Authentication Routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('me', [AuthController::class, 'me']);
        Route::put('update-password', [AuthController::class, 'updatePassword']);
    });

    // User Profile Routes
    Route::prefix('profile')->group(function () {
        Route::get('me', [UserProfileController::class, 'me']);
        Route::post('/', [UserProfileController::class, 'store']);
        Route::put('/', [UserProfileController::class, 'update']);
        Route::post('avatar', [UserProfileController::class, 'updateAvatar']);
        Route::get('completion', [UserProfileController::class, 'completionPercentage']);
    });

    // Assessment Routes
    Route::prefix('assessments')->group(function () {
        Route::post('{assessment}/start', [AssessmentController::class, 'start']);
        Route::get('{assessment}/questions', [AssessmentController::class, 'questions']);
        Route::get('{assessment}/statistics', [AssessmentController::class, 'statistics']);
    });

    // User Response Routes
    Route::prefix('responses')->group(function () {
        Route::get('/', [UserResponseController::class, 'index']);
        Route::post('/', [UserResponseController::class, 'store']);
        Route::post('submit-assessment', [UserResponseController::class, 'submitAssessment']);
        Route::get('assessment/{assessmentId}', [UserResponseController::class, 'getAssessmentResponses']);
        Route::get('{userResponse}', [UserResponseController::class, 'show']);
    });

    // Test Results Routes
    Route::prefix('test-results')->group(function () {
        Route::get('/', [TestResultController::class, 'index']);
        Route::get('{testResult}', [TestResultController::class, 'show']);
        Route::get('user/latest', [TestResultController::class, 'latest']);
        Route::get('assessment/{assessmentId}', [TestResultController::class, 'byAssessment']);
    });

    // Career Recommendations
    Route::get('career-recommendations', [CareerController::class, 'recommendations']);
    
    // Degree Recommendations  
    Route::get('degree-recommendations', [DegreeController::class, 'recommendations']);

    // User Blog Routes (for creating/editing own blogs)
    Route::prefix('my-blogs')->group(function () {
        Route::get('/', [BlogController::class, 'userBlogs']);
        Route::post('/', [BlogController::class, 'store']);
        Route::put('{blog}', [BlogController::class, 'update']);
        Route::delete('{blog}', [BlogController::class, 'destroy']);
    });
});

// Employer Routes
Route::prefix('v1/employer')->middleware(['auth:sanctum', 'role:employer'])->group(function () {
    Route::get('profile', [EmployerController::class, 'show']);
    Route::post('profile', [EmployerController::class, 'store']);
    Route::put('profile', [EmployerController::class, 'update']);
    
    // Employer can view user profiles for recruitment
    Route::get('candidates', [UserProfileController::class, 'index']);
    Route::get('candidates/{userProfile}', [UserProfileController::class, 'show']);
});

// Admin Routes
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'role:admin'])->group(function () {
    
    // User Management
    Route::get('users', [AdminUserController::class, 'index']);
    Route::get('users/stats', [AdminUserController::class, 'stats']);
    Route::get('users/roles', [AdminUserController::class, 'getRoles']);
    Route::get('users/{user}', [AdminUserController::class, 'show']);
    Route::put('users/{user}', [AdminUserController::class, 'update']);
    Route::delete('users/{user}', [AdminUserController::class, 'destroy']);
    Route::patch('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus']);
    Route::patch('users/{user}/assign-role', [AdminUserController::class, 'assignRole']);
    
    // Categories Management
    Route::apiResource('categories', CategoryController::class)->except(['index', 'show']);
    
    // Skills Management
    Route::apiResource('skills', SkillController::class)->except(['index', 'show']);
    
    // Careers Management
    Route::apiResource('careers', CareerController::class)->except(['index', 'show']);
    
    // Degrees Management
    Route::apiResource('degrees', DegreeController::class)->except(['index', 'show']);
    
    // Assessments Management
    Route::apiResource('assessments', AssessmentController::class)->except(['index', 'show']);
    
    // Questions Management
    Route::apiResource('questions', QuestionController::class);
    
    // Blog Management
    Route::apiResource('blogs', BlogController::class)->except(['index', 'show']);
    
    // Employer Management
    Route::get('employers', [EmployerController::class, 'index']);
    Route::put('employers/{employer}/verify', [EmployerController::class, 'verify']);
    Route::put('employers/{employer}/reject', [EmployerController::class, 'reject']);
    
    // Analytics & Reports
    Route::get('analytics/dashboard', [AdminController::class, 'dashboard']);
    Route::get('analytics/users', [AdminController::class, 'userAnalytics']);
    Route::get('analytics/assessments', [AdminController::class, 'assessmentAnalytics']);
    Route::get('analytics/careers', [AdminController::class, 'careerAnalytics']);
    
    // System Settings
    Route::get('settings', [AdminController::class, 'getSettings']);
    Route::put('settings', [AdminController::class, 'updateSettings']);
});

// Fallback route for undefined API endpoints
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => 'API endpoint not found'
    ], 404);
});
