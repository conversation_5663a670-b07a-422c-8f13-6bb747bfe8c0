<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations and seeders
        $this->artisan('migrate');
        $this->artisan('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
    }

    public function test_user_can_register()
    {
        $response = $this->postJson('/api/v1/auth/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'user' => ['id', 'name', 'email'],
                    'token',
                    'token_type'
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);
    }

    public function test_user_can_login()
    {
        // Create a user first
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123')
        ]);
        $user->assignRole('user');

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'user' => ['id', 'name', 'email'],
                    'token',
                    'token_type'
                ]);
    }

    public function test_user_can_access_protected_route()
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $response = $this->actingAs($user, 'sanctum')
                         ->getJson('/api/v1/auth/me');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'user' => ['id', 'name', 'email']
                ]);
    }

    public function test_unauthenticated_user_cannot_access_protected_route()
    {
        $response = $this->getJson('/api/v1/auth/me');

        $response->assertStatus(401);
    }

    public function test_public_routes_are_accessible()
    {
        $response = $this->getJson('/api/v1/categories');
        $response->assertStatus(200);

        $response = $this->getJson('/api/v1/careers');
        $response->assertStatus(200);

        $response = $this->getJson('/api/v1/assessments');
        $response->assertStatus(200);
    }

    public function test_admin_can_access_admin_routes()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin, 'sanctum')
                         ->getJson('/api/v1/admin/users');

        $response->assertStatus(200);
    }

    public function test_regular_user_cannot_access_admin_routes()
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $response = $this->actingAs($user, 'sanctum')
                         ->getJson('/api/v1/admin/users');

        $response->assertStatus(403);
    }
}
